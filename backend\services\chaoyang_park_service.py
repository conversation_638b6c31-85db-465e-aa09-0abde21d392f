"""
朝阳公园路径规划服务
支持三种策略：最短距离、最短时间、智能出行
支持三种出行方式：不限、步行、电瓶车
"""
from typing import List, Dict, Any, Tuple, Optional
import heapq
import math
from models.path_planning import Vertex2, Edge2

class ChaoyangParkService:
    """朝阳公园路径规划服务类"""

    def __init__(self):
        """初始化服务"""
        self.graph = None
        self.build_graph()

    def build_graph(self):
        """从数据库构建图"""
        self.graph = {}

        # 获取所有顶点
        vertices = Vertex2.query.all()
        print(f"朝阳公园顶点数量: {len(vertices)}")

        # 初始化图的邻接表
        for vertex in vertices:
            self.graph[vertex.vertex_id] = []

        # 获取所有边并填充邻接表
        edges = Edge2.query.all()
        print(f"朝阳公园边数量: {len(edges)}")

        for edge in edges:
            # 确保 src_id 和 des_id 存在于图中
            if edge.src_id not in self.graph:
                print(f"警告: 边的起点 {edge.src_id} 不在顶点中，添加它")
                self.graph[edge.src_id] = []

            if edge.des_id not in self.graph:
                print(f"警告: 边的终点 {edge.des_id} 不在顶点中，添加它")
                self.graph[edge.des_id] = []

            # 添加边到邻接表
            self.graph[edge.src_id].append({
                'dest_id': edge.des_id,
                'weight': edge.weight,
                'crowding': edge.crowding if edge.crowding is not None else 1.0,
                'is_car': edge.is_car if edge.is_car is not None else 0,
                'edge_id': edge.id
            })

            # 添加反向边（假设所有道路都是双向的）
            self.graph[edge.des_id].append({
                'dest_id': edge.src_id,
                'weight': edge.weight,
                'crowding': edge.crowding if edge.crowding is not None else 1.0,
                'is_car': edge.is_car if edge.is_car is not None else 0,
                'edge_id': edge.id
            })

        print(f"图构建完成，顶点数: {len(self.graph)}")

    def calculate_edge_weight(self, edge: Dict[str, Any], strategy: int, transport_mode: str) -> float:
        """
        根据策略和出行方式计算边的权重

        Args:
            edge: 边的信息字典
            strategy: 策略 (0: 最短距离, 1: 最短时间, 3: 智能出行)
            transport_mode: 出行方式 ('driving': 不限, 'walking': 步行, 'riding': 电瓶车)

        Returns:
            计算后的权重
        """
        distance = float(edge['weight'])
        crowding = edge['crowding']
        is_car = edge['is_car']  # 0: 步行道, 1: 电瓶车1线, 2: 电瓶车2线

        # 根据出行方式过滤可用道路
        if transport_mode == 'walking':
            # 步行模式：只能使用步行道
            if is_car != 0:
                return float('inf')  # 不可通行
        elif transport_mode == 'riding':
            # 电瓶车模式：只能使用电瓶车道
            if is_car == 0:
                return float('inf')  # 不可通行
        # driving模式（不限）：可以使用所有道路

        # 根据策略计算权重
        if strategy == 0:  # 最短距离
            return distance
        elif strategy == 1:  # 最短时间
            # 计算通行时间，考虑拥挤度
            if is_car == 0:  # 步行道
                speed = 4.0 * crowding  # 步行速度 4 km/h，受拥挤度影响
            else:  # 电瓶车道
                speed = 12.0 * crowding  # 电瓶车速度 12 km/h，受拥挤度影响

            # 时间 = 距离 / 速度 (转换为分钟)
            time_minutes = (distance / 1000.0) / speed * 60.0
            return time_minutes
        elif strategy == 3:  # 智能出行
            # 智能出行：优先选择电瓶车道，但也考虑时间效率
            if is_car == 0:  # 步行道
                speed = 4.0 * crowding
                time_minutes = (distance / 1000.0) / speed * 60.0
                return time_minutes  # 正常时间权重
            else:  # 电瓶车道
                speed = 12.0 * crowding
                time_minutes = (distance / 1000.0) / speed * 60.0
                return time_minutes * 0.3  # 电瓶车道权重大幅降低，更优先

        return distance

    def calculate_dijkstra_weight(self, edge: Dict[str, Any], strategy: int, transport_mode: str) -> float:
        """
        参照北邮版本的权重计算方法

        Args:
            edge: 边的信息字典
            strategy: 策略 (0: 最短距离, 1: 最短时间, 3: 智能出行)
            transport_mode: 出行方式 ('driving': 不限, 'walking': 步行, 'riding': 电瓶车)

        Returns:
            计算后的权重
        """
        distance = float(edge['weight'])
        crowding = edge['crowding'] if edge['crowding'] is not None and edge['crowding'] > 0 else 1.0
        is_car = edge['is_car'] if edge['is_car'] is not None else 0  # 0: 步行道, 1: 电瓶车1线, 2: 电瓶车2线

        # 根据出行方式过滤可用道路
        if transport_mode == 'walking':
            # 步行模式：只能使用步行道
            if is_car != 0:
                return float('inf')  # 不可通行
        elif transport_mode == 'riding':
            # 电瓶车模式：可以使用电瓶车道，但步行道权重很高
            if is_car == 0:
                # 允许使用步行道，但权重很高，作为备选
                pass
        # driving模式（不限）：可以使用所有道路

        # 根据策略计算权重 - 参照北邮版本
        if strategy == 0:  # 最短距离
            return distance
        elif strategy == 1:  # 最短时间
            # 计算通行时间，考虑拥挤度
            if is_car == 0:  # 步行道
                actual_speed = 4.0 * crowding  # 步行速度 4 km/h
                time_weight = distance / (actual_speed * 1000.0 / 60.0)  # 转换为分钟
                # 电瓶车模式下，步行道权重增加
                if transport_mode == 'riding':
                    time_weight *= 3.0  # 电瓶车模式下步行道权重增加
                return time_weight
            else:  # 电瓶车道
                actual_speed = 12.0 * crowding  # 电瓶车速度 12 km/h
                time_weight = distance / (actual_speed * 1000.0 / 60.0)  # 转换为分钟
                return time_weight
        elif strategy == 3:  # 智能出行 - 改进的智能出行策略
            if is_car == 0:  # 步行道
                actual_speed = 4.0 * crowding  # 步行速度 4 km/h
                time_weight = distance / (actual_speed * 1000.0 / 60.0)  # 转换为分钟
                return time_weight  # 正常时间权重
            else:  # 电瓶车道
                actual_speed = 12.0 * crowding  # 电瓶车速度 12 km/h
                time_weight = distance / (actual_speed * 1000.0 / 60.0)  # 转换为分钟
                return time_weight * 0.3  # 电瓶车道权重大幅降低，更优先

        return distance

    def dijkstra(self, start_id: int, end_id: int, strategy: int = 0, transport_mode: str = 'driving') -> Dict[str, Any]:
        """
        使用Dijkstra算法寻找最短路径 - 参照北邮版本实现

        Args:
            start_id: 起点ID
            end_id: 终点ID
            strategy: 策略 (0: 最短距离, 1: 最短时间, 3: 智能出行)
            transport_mode: 出行方式 ('driving': 不限, 'walking': 步行, 'riding': 电瓶车)

        Returns:
            包含距离和路径的字典
        """
        # 如果起点和终点相同，直接返回
        if start_id == end_id:
            return {
                'distance': 0,
                'path': [start_id]
            }

        # 确保图已经构建
        if not self.graph:
            self.build_graph()

        # 检查起点和终点是否在图中
        if start_id not in self.graph:
            return {'error': f'起点 {start_id} 不在图中'}

        if end_id not in self.graph:
            return {'error': f'终点 {end_id} 不在图中'}

        print(f"朝阳公园图大小: {len(self.graph)} 个顶点")
        print(f"起点 {start_id} 有 {len(self.graph[start_id])} 条边")
        print(f"终点 {end_id} 有 {len(self.graph[end_id])} 条边")

        # 初始化距离为无穷大
        distances = {vertex_id: float('infinity') for vertex_id in self.graph}
        distances[start_id] = 0

        # 初始化前驱顶点用于路径重建
        previous = {vertex_id: None for vertex_id in self.graph}

        # Dijkstra算法的优先队列
        priority_queue = [(0, start_id)]
        visited = set()

        while priority_queue:
            current_distance, current_vertex = heapq.heappop(priority_queue)

            # 如果已经访问过，跳过
            if current_vertex in visited:
                continue

            visited.add(current_vertex)

            # 如果到达目标，结束
            if current_vertex == end_id:
                break

            # 检查所有邻居
            for edge in self.graph[current_vertex]:
                neighbor = edge['dest_id']

                if neighbor in visited:
                    continue

                # 根据策略计算权重 - 参照北邮版本的权重计算方式
                weight = self.calculate_dijkstra_weight(edge, strategy, transport_mode)

                # 如果权重为无穷大，跳过这条边
                if weight == float('inf'):
                    continue

                # 计算通过当前顶点到邻居的距离
                distance = current_distance + weight

                # 如果找到更好的路径，更新
                if distance < distances[neighbor]:
                    distances[neighbor] = distance
                    previous[neighbor] = current_vertex
                    heapq.heappush(priority_queue, (distance, neighbor))

        print(f"访问了 {len(visited)} 个顶点，共 {len(self.graph)} 个")
        print(f"到终点 {end_id} 的距离: {distances[end_id]}")

        # 重建路径
        if distances[end_id] == float('infinity'):
            print(f"从 {start_id} 到 {end_id} 没有找到路径")
            return {
                'distance': float('infinity'),
                'path': []
            }

        path = []
        current = end_id
        while current is not None:
            path.append(current)
            current = previous[current]

        # 反转路径得到从起点到终点的路径
        path.reverse()

        print(f"找到路径: {path}")

        return {
            'distance': distances[end_id],
            'path': path
        }

    def get_shortest_path(self, start_id: int, end_id: int, strategy: int = 0, transport_mode: str = 'driving') -> Dict[str, Any]:
        """
        获取两点间的最短路径

        Args:
            start_id: 起点ID
            end_id: 终点ID
            strategy: 策略 (0: 最短距离, 1: 最短时间, 3: 智能出行)
            transport_mode: 出行方式 ('driving': 不限, 'walking': 步行, 'riding': 电瓶车)

        Returns:
            包含路径详情的字典
        """
        print(f"朝阳公园路径规划: start_id={start_id}, end_id={end_id}, strategy={strategy}, transport_mode={transport_mode}")

        # 如果起点和终点相同，直接返回
        if start_id == end_id:
            vertex = Vertex2.query.get(start_id)
            if not vertex:
                return {'error': f'顶点 {start_id} 不存在'}

            return {
                'path': [start_id],
                'path_details': [],
                'total_distance': 0,
                'total_time': 0,
                'cycling_distance': 0,
                'walking_distance': 0,
                'strategy': strategy,
                'transport_mode': transport_mode
            }

        # 使用Dijkstra算法找出最短路径
        result = self.dijkstra(start_id, end_id, strategy, transport_mode)

        # 如果找不到路径，返回错误
        if 'error' in result:
            return {'error': result['error']}

        if result['distance'] == float('infinity'):
            return {'error': '在指定的出行方式下，无法找到从起点到终点的路径'}

        path = result['path']
        path_details = []

        # 获取路径中的所有顶点
        try:
            vertices = {v.vertex_id: v for v in Vertex2.query.filter(Vertex2.vertex_id.in_(path)).all()}
        except Exception as e:
            print(f"查询顶点错误: {str(e)}")
            vertices = {}

        # 构建路径详情
        total_distance = 0
        total_time = 0
        cycling_distance = 0
        walking_distance = 0

        for i in range(len(path) - 1):
            source_id = path[i]
            dest_id = path[i + 1]

            try:
                # 查找边
                edge = Edge2.query.filter_by(src_id=source_id, des_id=dest_id).first()
                if not edge:
                    edge = Edge2.query.filter_by(src_id=dest_id, des_id=source_id).first()

                if edge:
                    # 获取顶点标签
                    from_label = vertices[source_id].label if source_id in vertices else f"地点 {source_id}"
                    to_label = vertices[dest_id].label if dest_id in vertices else f"地点 {dest_id}"

                    distance = edge.weight
                    crowding = edge.crowding if edge.crowding is not None else 1.0
                    is_car = edge.is_car if edge.is_car is not None else 0

                    # 计算时间
                    if is_car == 0:  # 步行道
                        speed = 4.0 * crowding  # km/h
                        travel_mode = 'walking'
                        walking_distance += distance
                    else:  # 电瓶车道
                        speed = 12.0 * crowding  # km/h
                        travel_mode = 'riding'
                        cycling_distance += distance

                    time_minutes = (distance / 1000.0) / speed * 60.0

                    path_details.append({
                        'from': from_label,
                        'to': to_label,
                        'distance': distance,
                        'time': time_minutes,
                        'crowding': crowding,
                        'travel_mode': travel_mode,
                        'is_car': is_car
                    })

                    total_distance += distance
                    total_time += time_minutes

                else:
                    # 如果找不到边，使用默认值
                    from_label = vertices[source_id].label if source_id in vertices else f"地点 {source_id}"
                    to_label = vertices[dest_id].label if dest_id in vertices else f"地点 {dest_id}"

                    # 计算欧几里得距离作为默认距离
                    try:
                        current_vertex = Vertex2.query.get(source_id)
                        next_vertex = Vertex2.query.get(dest_id)
                        if current_vertex and next_vertex:
                            distance = int(((current_vertex.x - next_vertex.x) ** 2 +
                                          (current_vertex.y - next_vertex.y) ** 2) ** 0.5)
                        else:
                            distance = 100
                    except:
                        distance = 100

                    # 默认为步行
                    speed = 4.0  # km/h
                    time_minutes = (distance / 1000.0) / speed * 60.0

                    path_details.append({
                        'from': from_label,
                        'to': to_label,
                        'distance': distance,
                        'time': time_minutes,
                        'crowding': 1.0,
                        'travel_mode': 'walking',
                        'is_car': 0
                    })

                    total_distance += distance
                    total_time += time_minutes
                    walking_distance += distance

            except Exception as e:
                print(f"处理边错误 {source_id} -> {dest_id}: {str(e)}")
                # 创建默认路径详情
                path_details.append({
                    'from': f"地点 {source_id}",
                    'to': f"地点 {dest_id}",
                    'distance': 100,
                    'time': 1.5,  # 默认1.5分钟
                    'crowding': 1.0,
                    'travel_mode': 'walking',
                    'is_car': 0
                })
                total_distance += 100
                total_time += 1.5
                walking_distance += 100

        return {
            'path': path,
            'path_details': path_details,
            'total_distance': total_distance,
            'total_time': total_time,
            'cycling_distance': cycling_distance,
            'walking_distance': walking_distance,
            'strategy': strategy,
            'transport_mode': transport_mode
        }

    def get_multi_destination_path(self, start_id: int, dest_ids: List[int], strategy: int = 0,
                                 transport_mode: str = 'driving', algorithm: str = 'auto') -> Dict[str, Any]:
        """
        获取多目标路径

        Args:
            start_id: 起点ID
            dest_ids: 目标点ID列表
            strategy: 策略 (0: 最短距离, 1: 最短时间, 3: 智能出行)
            transport_mode: 出行方式 ('driving': 不限, 'walking': 步行, 'riding': 电瓶车)
            algorithm: 算法选择 ('auto', 'simple')

        Returns:
            包含路径详情的字典
        """
        if not dest_ids:
            return {'error': '未指定目标点'}

        # 如果只有一个目标点，使用简单最短路径
        if len(dest_ids) == 1:
            return self.get_shortest_path(start_id, dest_ids[0], strategy, transport_mode)

        # 对于多个目标点，使用简单的顺序访问策略
        # 创建包含起点和所有目标点的列表
        all_vertices = [start_id] + dest_ids

        # 构建完整路径
        complete_path = []
        total_distance = 0
        total_time = 0
        cycling_distance = 0
        walking_distance = 0
        path_details = []

        # 按顺序访问每个目标点
        for i in range(len(all_vertices) - 1):
            current = all_vertices[i]
            next_vertex = all_vertices[i + 1]

            # 获取两点间的最短路径
            result = self.get_shortest_path(current, next_vertex, strategy, transport_mode)

            if 'error' in result:
                return {'error': f'无法找到从 {current} 到 {next_vertex} 的路径: {result["error"]}'}

            # 合并路径
            if i == 0:
                # 第一段路径，包含起点
                complete_path.extend(result['path'])
            else:
                # 后续路径，跳过起点（避免重复）
                complete_path.extend(result['path'][1:])

            # 累加距离和时间
            total_distance += result['total_distance']
            total_time += result['total_time']
            cycling_distance += result['cycling_distance']
            walking_distance += result['walking_distance']

            # 合并路径详情
            path_details.extend(result['path_details'])

        return {
            'path': complete_path,
            'path_details': path_details,
            'total_distance': total_distance,
            'total_time': total_time,
            'cycling_distance': cycling_distance,
            'walking_distance': walking_distance,
            'algorithm': 'simple',
            'strategy': strategy,
            'transport_mode': transport_mode
        }
